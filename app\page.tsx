"use client"

import { useState } from "react"
import BannerSlider from "./components/BannerSlider"
import CategorySection from "./components/CategorySection"
import PromoBanner from "./components/PromoBanner"
import PaginatedProductSection from "./components/PaginatedProductSection"
import { useData } from "./contexts/DataContext"

export default function HomePage() {
  // Use centralized data context
  const { products, homepageSections, banners, currentUser, isLoading, refreshData } = useData()
  const userRole = currentUser?.role || "user"

  // Debug logging
  console.log('🏠 HomePage render:', {
    productsCount: products.length,
    sectionsCount: homepageSections.length,
    bannersCount: banners.length,
    isLoading
  })

  // Force refresh data on mount
  useEffect(() => {
    console.log('🔄 HomePage mounted, forcing data refresh...')
    refreshData()
  }, [])

  // Get active homepage sections sorted by order
  const activeSections = homepageSections.filter((section) => section.active).sort((a, b) => a.order - b.order)

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-white mt-4">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Debug Info */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-yellow-900 text-yellow-100 p-2 text-sm">
          Products: {products.length} | Sections: {homepageSections.length} | Banners: {banners.length}
        </div>
      )}

      {/* Banner Slider */}
      <section className="container mx-auto px-4 pt-4 pb-2">
        <BannerSlider banners={banners} />
      </section>

      {/* Compact Promotional Banner */}
      <section className="container mx-auto px-4 pt-2 pb-2">
        <PromoBanner />
      </section>

      {/* Main Product Catalog */}
      <div className="container mx-auto px-4 py-4">
        <PaginatedProductSection
          title="🎮 جميع المنتجات"
          products={products}
          userRole={userRole}
          productsPerPage={20}
        />
      </div>

      {/* Featured Sections (Optional - can be removed if not needed) */}
      <div className="container mx-auto px-4 pb-8">
        {activeSections.slice(0, 2).map((section) => {
          const sectionProducts = products.filter((product) => section.productIds.includes(product.id))

          if (sectionProducts.length === 0) return null

          return (
            <CategorySection
              key={section.id}
              title={`${section.emoji || ""} ${section.title}`.trim()}
              products={sectionProducts.slice(0, 8)} // Limit to 8 products for featured sections
              userRole={userRole}
            />
          )
        })}
      </div>
    </div>
  )
}
