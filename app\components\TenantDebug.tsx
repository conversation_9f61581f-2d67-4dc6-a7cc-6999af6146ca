"use client"

import { useEffect, useState } from "react"
import { TenantResolver } from "../lib/tenant"
import { supabase } from "../lib/supabase"

export default function TenantDebug() {
  const [debugInfo, setDebugInfo] = useState<any>({})
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const runDebug = async () => {
      const info: any = {
        timestamp: new Date().toISOString(),
        environment: {
          NODE_ENV: process.env.NODE_ENV,
          NEXT_PUBLIC_MULTI_TENANT_MODE: process.env.NEXT_PUBLIC_MULTI_TENANT_MODE,
          NEXT_PUBLIC_DEFAULT_TENANT_SLUG: process.env.NEXT_PUBLIC_DEFAULT_TENANT_SLUG,
          NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + '...',
        },
        supabaseConnection: null,
        tenantQuery: null,
        tenantResolution: null,
        errors: []
      }

      try {
        // Test Supabase connection
        console.log('🔗 Testing Supabase connection...')
        const { data: connectionTest, error: connectionError } = await supabase
          .from('tenants')
          .select('count')
          .limit(1)
        
        info.supabaseConnection = {
          success: !connectionError,
          error: connectionError?.message,
          data: connectionTest
        }

        // Test direct tenant query
        console.log('🔍 Testing direct tenant query...')
        const { data: tenantData, error: tenantError } = await supabase
          .from('tenants')
          .select('*')
          .eq('slug', 'main')
          .single()

        info.tenantQuery = {
          success: !tenantError,
          error: tenantError?.message,
          data: tenantData ? {
            id: tenantData.id,
            name: tenantData.name,
            slug: tenantData.slug,
            status: tenantData.status
          } : null
        }

        // Test tenant resolution methods
        console.log('🏢 Testing tenant resolution...')
        
        try {
          const defaultTenant = await TenantResolver.getDefaultTenant()
          info.tenantResolution = {
            defaultTenant: defaultTenant ? {
              id: defaultTenant.id,
              name: defaultTenant.name,
              slug: defaultTenant.slug,
              status: defaultTenant.status
            } : null
          }
        } catch (err) {
          info.tenantResolution = {
            error: err instanceof Error ? err.message : 'Unknown error'
          }
        }

      } catch (err) {
        console.error('❌ Debug error:', err)
        info.errors.push(err instanceof Error ? err.message : 'Unknown error')
      }

      setDebugInfo(info)
      setIsLoading(false)
    }

    runDebug()
  }, [])

  if (isLoading) {
    return (
      <div className="fixed top-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-md">
        <h3 className="font-bold mb-2">🔍 Tenant Debug</h3>
        <p>Running diagnostics...</p>
      </div>
    )
  }

  return (
    <div className="fixed top-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-md max-h-96 overflow-y-auto text-xs">
      <h3 className="font-bold mb-2">🔍 Tenant Debug Info</h3>
      
      <div className="space-y-2">
        <div>
          <strong>Environment:</strong>
          <pre className="bg-gray-900 p-2 rounded mt-1 text-xs">
            {JSON.stringify(debugInfo.environment, null, 2)}
          </pre>
        </div>

        <div>
          <strong>Supabase Connection:</strong>
          <pre className="bg-gray-900 p-2 rounded mt-1 text-xs">
            {JSON.stringify(debugInfo.supabaseConnection, null, 2)}
          </pre>
        </div>

        <div>
          <strong>Direct Tenant Query:</strong>
          <pre className="bg-gray-900 p-2 rounded mt-1 text-xs">
            {JSON.stringify(debugInfo.tenantQuery, null, 2)}
          </pre>
        </div>

        <div>
          <strong>Tenant Resolution:</strong>
          <pre className="bg-gray-900 p-2 rounded mt-1 text-xs">
            {JSON.stringify(debugInfo.tenantResolution, null, 2)}
          </pre>
        </div>

        {debugInfo.errors.length > 0 && (
          <div>
            <strong>Errors:</strong>
            <pre className="bg-red-900 p-2 rounded mt-1 text-xs">
              {JSON.stringify(debugInfo.errors, null, 2)}
            </pre>
          </div>
        )}
      </div>

      <button
        onClick={() => window.location.reload()}
        className="mt-2 px-2 py-1 bg-blue-600 rounded text-xs"
      >
        Refresh
      </button>
    </div>
  )
}
