import { supabase } from './supabase'
import { config } from './config'

export interface Tenant {
  id: string
  name: string
  slug: string
  custom_domain?: string
  theme_config: {
    primaryColor: string
    secondaryColor: string
    accentColor: string
    backgroundColor: string
    textColor: string
    logo?: string
    favicon?: string
    customCSS?: string
  }
  settings: {
    features: {
      digitalCodes: boolean
      walletSystem: boolean
      customFields: boolean
      analytics: boolean
    }
    limits: {
      maxProducts: number
      maxUsers: number
      maxOrders: number
    }
  }
  status: 'active' | 'inactive' | 'suspended'
  created_at: string
  updated_at: string
}

/**
 * Resolves tenant from various sources with fallback strategies
 */
export class TenantResolver {
  private static cache = new Map<string, Tenant>()
  private static cacheExpiry = new Map<string, number>()
  private static readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes

  /**
   * Resolve tenant from request headers (domain/subdomain)
   */
  static async resolveTenantFromRequest(request: Request): Promise<Tenant | null> {
    const url = new URL(request.url)
    const host = request.headers.get('host') || url.host

    // Try custom domain first
    let tenant = await this.getTenantByCustomDomain(host)
    if (tenant) return tenant

    // Try subdomain extraction
    const subdomain = this.extractSubdomain(host)
    if (subdomain && subdomain !== 'www') {
      tenant = await this.getTenantBySlug(subdomain)
      if (tenant) return tenant
    }

    // Fallback to default tenant
    return await this.getDefaultTenant()
  }

  /**
   * Resolve tenant from environment or default
   */
  static async resolveTenantFromEnvironment(): Promise<Tenant | null> {
    const defaultTenantId = process.env.NEXT_PUBLIC_DEFAULT_TENANT_ID
    const defaultTenantSlug = process.env.NEXT_PUBLIC_DEFAULT_TENANT_SLUG || 'main'

    console.log('🌍 Environment tenant config:', { defaultTenantId, defaultTenantSlug })

    // Test Supabase connection first
    try {
      const { data, error } = await supabase.from('tenants').select('count').limit(1)
      console.log('🔗 Supabase connection test:', { success: !error, error: error?.message })
    } catch (err) {
      console.error('❌ Supabase connection failed:', err)
    }

    if (defaultTenantId) {
      console.log('🔍 Resolving by environment tenant ID:', defaultTenantId)
      return await this.getTenantById(defaultTenantId)
    }

    console.log('🔍 Resolving by environment tenant slug:', defaultTenantSlug)
    return await this.getTenantBySlug(defaultTenantSlug)
  }

  /**
   * Get tenant by custom domain
   */
  static async getTenantByCustomDomain(domain: string): Promise<Tenant | null> {
    const cacheKey = `domain:${domain}`
    const cached = this.getCachedTenant(cacheKey)
    if (cached) return cached

    try {
      const { data, error } = await supabase
        .from('tenants')
        .select('*')
        .eq('custom_domain', domain)
        .eq('status', 'active')
        .single()

      if (error || !data) return null

      const tenant = this.mapDatabaseToTenant(data)
      this.setCachedTenant(cacheKey, tenant)
      return tenant
    } catch (error) {
      console.error('Error fetching tenant by custom domain:', error)
      return null
    }
  }

  /**
   * Get tenant by slug
   */
  static async getTenantBySlug(slug: string): Promise<Tenant | null> {
    const cacheKey = `slug:${slug}`
    const cached = this.getCachedTenant(cacheKey)
    if (cached) {
      console.log('🎯 Found cached tenant by slug:', slug)
      return cached
    }

    try {
      console.log('🔍 Querying database for tenant slug:', slug)
      const { data, error } = await supabase
        .from('tenants')
        .select('*')
        .eq('slug', slug)
        .eq('status', 'active')
        .single()

      console.log('📊 Database response:', { data: !!data, error: error?.message })

      if (error) {
        console.error('❌ Database error:', error)
        return null
      }

      if (!data) {
        console.warn('⚠️ No tenant found with slug:', slug)
        return null
      }

      const tenant = this.mapDatabaseToTenant(data)
      this.setCachedTenant(cacheKey, tenant)
      console.log('✅ Successfully resolved tenant:', tenant.name)
      return tenant
    } catch (error) {
      console.error('❌ Error fetching tenant by slug:', error)
      return null
    }
  }

  /**
   * Get tenant by ID
   */
  static async getTenantById(id: string): Promise<Tenant | null> {
    const cacheKey = `id:${id}`
    const cached = this.getCachedTenant(cacheKey)
    if (cached) return cached

    try {
      const { data, error } = await supabase
        .from('tenants')
        .select('*')
        .eq('id', id)
        .eq('status', 'active')
        .single()

      if (error || !data) return null

      const tenant = this.mapDatabaseToTenant(data)
      this.setCachedTenant(cacheKey, tenant)
      return tenant
    } catch (error) {
      console.error('Error fetching tenant by ID:', error)
      return null
    }
  }

  /**
   * Get default tenant (main)
   */
  static async getDefaultTenant(): Promise<Tenant | null> {
    console.log('🏠 Getting default tenant (main)...')
    const tenant = await this.getTenantBySlug('main')
    if (tenant) {
      console.log('✅ Default tenant found:', tenant.name)
    } else {
      console.error('❌ Default tenant not found!')
    }
    return tenant
  }

  /**
   * Extract subdomain from host
   */
  private static extractSubdomain(host: string): string | null {
    // Remove port if present
    const hostname = host.split(':')[0]
    const parts = hostname.split('.')
    
    // For localhost or IP addresses, no subdomain
    if (parts.length < 3 || hostname === 'localhost' || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
      return null
    }

    return parts[0]
  }

  /**
   * Map database record to Tenant interface
   */
  private static mapDatabaseToTenant(data: any): Tenant {
    return {
      id: data.id,
      name: data.name,
      slug: data.slug,
      custom_domain: data.custom_domain,
      theme_config: data.theme_config || {},
      settings: data.settings || {},
      status: data.status,
      created_at: data.created_at,
      updated_at: data.updated_at,
    }
  }

  /**
   * Cache management
   */
  private static getCachedTenant(key: string): Tenant | null {
    const expiry = this.cacheExpiry.get(key)
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(key)
      this.cacheExpiry.delete(key)
      return null
    }
    return this.cache.get(key) || null
  }

  private static setCachedTenant(key: string, tenant: Tenant): void {
    this.cache.set(key, tenant)
    this.cacheExpiry.set(key, Date.now() + this.CACHE_TTL)
  }

  /**
   * Clear cache (useful for testing or when tenant data changes)
   */
  static clearCache(): void {
    this.cache.clear()
    this.cacheExpiry.clear()
  }
}

/**
 * Utility functions for tenant operations
 */
export const tenantUtils = {
  /**
   * Check if multi-tenant mode is enabled
   */
  isMultiTenantMode(): boolean {
    return process.env.NEXT_PUBLIC_MULTI_TENANT_MODE === 'true'
  },

  /**
   * Get tenant-aware database query filter
   */
  getTenantFilter(tenantId: string) {
    return { tenant_id: tenantId }
  },

  /**
   * Validate tenant access for user
   */
  async validateTenantAccess(userId: string, tenantId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('tenant_id, role, settings')
        .eq('id', userId)
        .single()

      if (error || !data) return false

      // Super admin can access any tenant
      if (data.settings?.is_super_admin) return true

      // Regular users can only access their own tenant
      return data.tenant_id === tenantId
    } catch (error) {
      console.error('Error validating tenant access:', error)
      return false
    }
  },

  /**
   * Apply tenant theme to CSS variables
   */
  applyTenantTheme(theme: Tenant['theme_config']): void {
    if (typeof document === 'undefined') return

    const root = document.documentElement
    root.style.setProperty('--tenant-primary', theme.primaryColor || '#3b82f6')
    root.style.setProperty('--tenant-secondary', theme.secondaryColor || '#1e40af')
    root.style.setProperty('--tenant-accent', theme.accentColor || '#f59e0b')
    root.style.setProperty('--tenant-background', theme.backgroundColor || '#111827')
    root.style.setProperty('--tenant-text', theme.textColor || '#ffffff')

    // Apply custom CSS if provided
    if (theme.customCSS) {
      let styleElement = document.getElementById('tenant-custom-css')
      if (!styleElement) {
        styleElement = document.createElement('style')
        styleElement.id = 'tenant-custom-css'
        document.head.appendChild(styleElement)
      }
      styleElement.textContent = theme.customCSS
    }
  }
}
